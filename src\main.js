/**
 * Goblin Survivors - Main Entry Point
 * A top-down 2D horde survival game with D&D themes
 */

import { Game } from './engine/Game.js';
import { MenuScene } from './scenes/MenuScene.js';
import { GameScene } from './scenes/GameScene.js';
import { TownScene } from './scenes/TownScene.js';

// Game configuration
const CONFIG = {
    width: 1024,
    height: 768,
    backgroundColor: 0x2d5016, // Forest green
    antialias: false,
    resolution: 1
};

// Initialize the game
async function init() {
    try {
        // Hide loading screen after a short delay to show it briefly
        setTimeout(() => {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.classList.add('hidden');
            }
        }, 1500);

        // Create game instance
        const game = new Game(CONFIG);
        
        // Register scenes
        game.sceneManager.addScene('menu', new MenuScene(game));
        game.sceneManager.addScene('game', new GameScene(game));
        game.sceneManager.addScene('town', new TownScene(game));
        
        // Start with menu scene
        await game.start('menu');
        
        console.log('🎮 Goblin Survivors initialized successfully!');
        console.log('🎲 May your dice rolls be high and your goblins be many!');

        // Debug: Add performance monitoring
        setInterval(() => {
            if (game.isRunning) {
                game.showPerformanceInfo();
            }
        }, 5000); // Every 5 seconds
        
    } catch (error) {
        console.error('❌ Failed to initialize Goblin Survivors:', error);
        
        // Show error message to user
        const loading = document.getElementById('loading');
        if (loading) {
            loading.innerHTML = `
                <div style="color: #ff4444;">
                    <h3>Failed to Load Game</h3>
                    <p>Error: ${error.message}</p>
                    <p>Please refresh the page to try again.</p>
                </div>
            `;
        }
    }
}

// Start the game when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}

// Global error handling
window.addEventListener('error', (event) => {
    console.error('🚨 Unhandled error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('🚨 Unhandled promise rejection:', event.reason);
});

// Export for debugging
window.GoblinSurvivors = {
    version: '1.0.0',
    config: CONFIG
};
