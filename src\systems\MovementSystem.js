/**
 * Movement System
 * Handles entity movement based on velocity
 */

import { System } from '../engine/ECS.js';
import { Transform } from '../components/Transform.js';
import { Velocity } from '../components/Velocity.js';

export class MovementSystem extends System {
    constructor() {
        super();
        this.requiredComponents = [Transform, Velocity];
    }
    
    getRequiredComponents() {
        return this.requiredComponents;
    }
    
    updateEntity(entity, deltaTime) {
        const transform = entity.getComponent(Transform);
        const velocity = entity.getComponent(Velocity);
        
        if (!transform || !velocity) return;
        
        // Apply friction and air resistance
        velocity.applyFriction(deltaTime);
        velocity.applyAirResistance(deltaTime);
        
        // Update position based on velocity
        const deltaX = velocity.x * deltaTime;
        const deltaY = velocity.y * deltaTime;
        
        transform.translate(deltaX, deltaY);
    }
}
