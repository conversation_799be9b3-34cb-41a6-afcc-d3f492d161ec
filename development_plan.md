# Goblin Survivors - JavaScript Development Plan

## 🎮 Project Overview

Based on the game design document, this is a comprehensive development plan for building **Goblin Survivors** - a top-down 2D horde survival game with D&D themes, inspired by Vampire Survivors. The game features auto-attacking combat, rogue-lite progression, and comedic tabletop RPG elements.

## 📋 Development Phases Overview

### Phase 1: Project Setup & Architecture (4 tasks)
- **Initialize Project Structure**: Create directory structure, package.json, and basic HTML/CSS files
- **Set Up Build Tools**: Configure Webpack/Vite, ESLint, and development server for modern JavaScript development
- **Choose Graphics Library**: Select and integrate graphics library (Canvas API, PixiJS, or Phaser) for 2D rendering
- **Design Core Architecture**: Plan entity-component system, game state management, and module organization

### Phase 2: Core Game Engine (5 tasks)
- **Game Loop Implementation**: Create main game loop with fixed timestep, update/render cycles, and frame rate management
- **Input System**: Implement keyboard and mouse input handling with event management
- **Scene Management**: Create scene system for transitioning between town, gameplay, and menu states
- **Entity Component System**: Build ECS architecture for managing game objects, components, and systems
- **Camera System**: Implement 2D camera with player following and screen boundaries

### Phase 3: Player Character System (5 tasks)
- **Player Entity Creation**: Create player character entity with position, sprite, and basic properties
- **Movement System**: Implement WASD/arrow key movement with collision detection
- **Player Stats System**: Create D&D-style stats (HP, damage, speed, defense) with modifiers
- **Health & Death System**: Implement health management, damage taking, and death/respawn mechanics
- **Player Animation**: Add sprite animations for idle, walking, and attacking states

### Phase 4: Enemy System (5 tasks)
- **Basic Goblin Entity**: Create basic goblin grunt with sprite, health, and movement toward player
- **Enemy AI System**: Implement pathfinding and behavior for different enemy types
- **Enemy Spawning System**: Create wave-based spawning with increasing difficulty over time
- **Enemy Types Implementation**: Add goblin archer, bomber, shaman, and elite variants with unique behaviors
- **Boss Enemies**: Implement Goblin King, Troll, and Gelatinous Cube boss encounters

### Phase 5: Combat & Weapons System (5 tasks)
- **Auto-Attack System**: Implement automatic attacking based on weapon range and cooldowns
- **Basic Weapons**: Create sword, dagger, crossbow with different attack patterns and ranges
- **Damage Calculation**: Implement damage system with crits, armor, and D&D-style dice rolling
- **Special Abilities**: Add whirlwind, shield bash, battle cry, and other special attacks
- **Weapon Synergies**: Create system for combining weapons and abilities for enhanced effects

### Phase 6: Level Up & Power-up System (5 tasks)
- **XP Collection System**: Create D&D-style glowing dice XP drops that players can collect
- **Level Up Mechanics**: Implement experience tracking, level progression, and level-up triggers
- **Power-up Selection UI**: Create interface for choosing between 3 random power-ups on level up
- **Passive Abilities**: Implement health potions, lucky loot, speed training, and crit chance
- **Power-up Stacking**: Create system for stacking and synergizing multiple abilities

### Phase 7: Town & Metaprogression (5 tasks)
- **Town Scene Creation**: Build the town hub area with buildings and interactive zones
- **NPC System**: Create blacksmith, cleric, trainer, wizard, and other town NPCs with dialogue
- **Currency System**: Implement gold, gems, and goblin parts as persistent currencies
- **Permanent Upgrades**: Create stat upgrades, weapon mastery, and equipment unlocks
- **Save System**: Implement local storage for persistent progression and town state

### Phase 8: Game States & UI (5 tasks)
- **Game State Manager**: Create state machine for menu, town, gameplay, and pause states
- **Main Menu**: Design and implement start screen with new game, continue, and options
- **HUD System**: Create in-game UI showing health, XP, level, timer, and current weapons
- **Pause Menu**: Implement pause functionality with resume, settings, and quit options
- **Game Over Screen**: Create death/victory screens with stats summary and return to town

### Phase 9: Audio & Visual Polish (5 tasks)
- **Audio System**: Implement audio manager for sound effects and background music
- **Particle Effects**: Create visual effects for attacks, deaths, level-ups, and environmental elements
- **Screen Shake & Juice**: Add screen shake, hit effects, and game feel improvements
- **Sprite Art & Animation**: Create or integrate pixel art sprites and animations for all game elements
- **UI Polish**: Add visual polish to menus, buttons, and interface elements

### Phase 10: Testing & Balancing (5 tasks)
- **Unit Testing Setup**: Set up Jest or similar testing framework for game logic testing
- **Gameplay Balancing**: Tune enemy spawn rates, damage values, XP progression, and difficulty curves
- **Performance Optimization**: Optimize rendering, collision detection, and memory usage for smooth gameplay
- **Bug Testing & Fixes**: Comprehensive testing of all game systems and fixing identified issues
- **Deployment Preparation**: Prepare build process, minification, and deployment for web hosting

## 🛠️ Technology Recommendations

### Graphics Library Options
- **PixiJS**: Best for performance and advanced 2D features, excellent sprite handling
- **Phaser 3**: Great for rapid prototyping with built-in physics and extensive documentation
- **Canvas API**: Lightweight, full control, good for learning and custom implementations

### Build Tools
- **Vite**: Fast development server, modern bundling, excellent for modern JavaScript
- **Webpack**: More configuration options, industry standard, extensive plugin ecosystem

### Additional Libraries
- **Howler.js**: Comprehensive audio management with Web Audio API support
- **Matter.js** or **Box2D**: Physics engines (if advanced collision detection needed)
- **Jest**: Unit testing framework for JavaScript
- **ESLint + Prettier**: Code quality and formatting tools

## 🎯 Key Development Priorities

### 1. Start with Minimum Viable Product (MVP)
Focus on core gameplay loop first:
- Basic player movement
- Simple enemy spawning and AI
- Auto-attacking combat
- Basic XP collection and leveling

### 2. Iterative Development Approach
- Get each system working independently before integration
- Test frequently during development
- Build vertical slices of functionality

### 3. D&D Theme Integration
- Ensure humor and D&D references are woven throughout development
- Use dice-based visual effects for crits and random events
- Implement stat system that feels authentically D&D-inspired

### 4. Performance Considerations
- Optimize for 60 FPS with hundreds of enemies on screen
- Use object pooling for frequently created/destroyed objects
- Implement efficient collision detection systems

## 📅 Estimated Timeline

### Development Phases
- **MVP (Phases 1-5)**: 4-6 weeks
  - Core gameplay loop functional
  - Basic combat and enemy systems
  - Simple progression mechanics

- **Feature Complete (Phases 1-8)**: 8-10 weeks
  - All major game systems implemented
  - Town hub and metaprogression working
  - Complete UI and game state management

- **Polished Release (All Phases)**: 12-14 weeks
  - Audio and visual polish complete
  - Balanced and optimized gameplay
  - Ready for deployment

### Weekly Breakdown (Suggested)
- **Week 1-2**: Project setup, core engine, basic player
- **Week 3-4**: Enemy system and basic combat
- **Week 5-6**: Level up system and power-ups
- **Week 7-8**: Town hub and metaprogression
- **Week 9-10**: UI polish and game states
- **Week 11-12**: Audio, visual effects, and balancing
- **Week 13-14**: Testing, optimization, and deployment

## 📁 Recommended Project Structure

```
goblin-survivors/
├── src/
│   ├── engine/
│   │   ├── Game.js
│   │   ├── Scene.js
│   │   ├── Input.js
│   │   └── Camera.js
│   ├── entities/
│   │   ├── Player.js
│   │   ├── Enemy.js
│   │   └── Projectile.js
│   ├── systems/
│   │   ├── MovementSystem.js
│   │   ├── CombatSystem.js
│   │   └── RenderSystem.js
│   ├── components/
│   │   ├── Transform.js
│   │   ├── Health.js
│   │   └── Weapon.js
│   ├── scenes/
│   │   ├── MenuScene.js
│   │   ├── GameScene.js
│   │   └── TownScene.js
│   ├── ui/
│   │   ├── HUD.js
│   │   ├── Menu.js
│   │   └── PowerUpSelection.js
│   ├── audio/
│   │   └── AudioManager.js
│   ├── utils/
│   │   ├── Math.js
│   │   └── Storage.js
│   └── main.js
├── assets/
│   ├── sprites/
│   ├── audio/
│   └── fonts/
├── tests/
├── dist/
├── package.json
├── webpack.config.js (or vite.config.js)
└── index.html
```

## 🎮 Core Game Features Checklist

### Essential Features (MVP)
- [ ] Player movement (WASD)
- [ ] Basic goblin enemies
- [ ] Auto-attacking combat
- [ ] XP collection and leveling
- [ ] Basic power-up selection
- [ ] Simple spawning system

### Core Features
- [ ] Multiple weapon types
- [ ] Enemy variety (archer, bomber, shaman)
- [ ] Town hub with NPCs
- [ ] Permanent upgrades
- [ ] Save/load system
- [ ] Boss encounters

### Polish Features
- [ ] Particle effects
- [ ] Screen shake and juice
- [ ] Audio system
- [ ] Sprite animations
- [ ] UI polish
- [ ] Performance optimization

## 🚀 Getting Started

1. **Set up development environment**
2. **Choose your graphics library**
3. **Create basic project structure**
4. **Implement core game loop**
5. **Build MVP features incrementally**

Remember: Start small, test often, and iterate based on gameplay feel. The D&D humor and theme should be integrated throughout development, not added as an afterthought.

## 📝 Development Notes

### Key Design Decisions to Make Early
- **Graphics Library**: PixiJS vs Phaser vs Canvas API
- **State Management**: How to handle game state transitions
- **Entity System**: ECS vs traditional OOP approach
- **Asset Pipeline**: How to handle sprites, audio, and other assets

### Performance Targets
- **60 FPS** with 200+ enemies on screen
- **< 100ms** load times for scene transitions
- **< 50MB** total asset size for web deployment

### Testing Strategy
- Unit tests for game logic (damage calculation, XP progression)
- Integration tests for system interactions
- Manual playtesting for balance and fun factor
- Performance testing with large enemy counts

This development plan provides a structured approach to building your Goblin Survivors game while maintaining flexibility for iteration and improvement during development.
