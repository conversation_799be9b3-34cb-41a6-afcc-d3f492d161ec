/**
 * Player Component
 * Marks an entity as the player and stores player-specific data
 */

import { Component } from '../engine/ECS.js';

export class Player extends Component {
    constructor() {
        super();
        
        // D&D-style stats
        this.level = 1;
        this.experience = 0;
        this.experienceToNext = 100;
        
        // Base stats
        this.strength = 10;     // Affects damage
        this.endurance = 10;    // Affects health
        this.agility = 10;      // Affects speed
        this.luck = 10;         // Affects crits and drops
        
        // Derived stats
        this.damage = 10;
        this.speed = 200;
        this.critChance = 0.05; // 5%
        this.critMultiplier = 2.0;
        
        // Equipment and abilities
        this.weapons = [];
        this.abilities = [];
        this.passives = [];
        
        // Currency
        this.gold = 0;
        this.gems = 0;
        this.goblinParts = 0;
        
        // Game state
        this.killCount = 0;
        this.survivalTime = 0;
        
        this.calculateDerivedStats();
    }
    
    // Level up
    levelUp() {
        this.level++;
        this.experience -= this.experienceToNext;
        this.experienceToNext = Math.floor(this.experienceToNext * 1.2); // 20% increase
        
        // Increase base stats slightly
        this.strength += 1;
        this.endurance += 1;
        this.agility += 1;
        
        this.calculateDerivedStats();
        
        console.log(`🎉 Level up! Now level ${this.level}`);
        return true;
    }
    
    // Add experience
    addExperience(amount) {
        this.experience += amount;
        
        let leveledUp = false;
        while (this.experience >= this.experienceToNext) {
            this.levelUp();
            leveledUp = true;
        }
        
        return leveledUp;
    }
    
    // Calculate derived stats from base stats
    calculateDerivedStats() {
        // Damage based on strength
        this.damage = 10 + (this.strength - 10) * 2;
        
        // Speed based on agility
        this.speed = 150 + (this.agility - 10) * 10;
        
        // Crit chance based on luck
        this.critChance = 0.05 + (this.luck - 10) * 0.01;
        this.critChance = Math.max(0, Math.min(0.95, this.critChance)); // Cap at 95%
        
        // Update health component if it exists
        if (this.entity) {
            const health = this.entity.getComponent(Health);
            if (health) {
                const newMaxHealth = 100 + (this.endurance - 10) * 10;
                health.setMaxHealth(newMaxHealth);
            }
            
            // Update velocity component if it exists
            const velocity = this.entity.getComponent(Velocity);
            if (velocity) {
                velocity.maxSpeed = this.speed;
            }
        }
    }
    
    // Add weapon
    addWeapon(weapon) {
        this.weapons.push(weapon);
        console.log(`⚔️ Acquired weapon: ${weapon.name}`);
    }
    
    // Add ability
    addAbility(ability) {
        this.abilities.push(ability);
        console.log(`✨ Learned ability: ${ability.name}`);
    }
    
    // Add passive
    addPassive(passive) {
        this.passives.push(passive);
        console.log(`🔮 Gained passive: ${passive.name}`);
    }
    
    // Add currency
    addGold(amount) {
        this.gold += amount;
    }
    
    addGems(amount) {
        this.gems += amount;
    }
    
    addGoblinParts(amount) {
        this.goblinParts += amount;
    }
    
    // Spend currency
    spendGold(amount) {
        if (this.gold >= amount) {
            this.gold -= amount;
            return true;
        }
        return false;
    }
    
    spendGems(amount) {
        if (this.gems >= amount) {
            this.gems -= amount;
            return true;
        }
        return false;
    }
    
    spendGoblinParts(amount) {
        if (this.goblinParts >= amount) {
            this.goblinParts -= amount;
            return true;
        }
        return false;
    }
    
    // Increase stats
    increaseStat(statName, amount = 1) {
        switch (statName.toLowerCase()) {
            case 'strength':
                this.strength += amount;
                break;
            case 'endurance':
                this.endurance += amount;
                break;
            case 'agility':
                this.agility += amount;
                break;
            case 'luck':
                this.luck += amount;
                break;
        }
        this.calculateDerivedStats();
    }
    
    // Get experience percentage
    getExperiencePercentage() {
        return this.experienceToNext > 0 ? this.experience / this.experienceToNext : 0;
    }
    
    // Check if can level up
    canLevelUp() {
        return this.experience >= this.experienceToNext;
    }
    
    // Get total stat points
    getTotalStatPoints() {
        return this.strength + this.endurance + this.agility + this.luck;
    }
    
    // Get player summary
    getSummary() {
        return {
            level: this.level,
            experience: this.experience,
            experienceToNext: this.experienceToNext,
            stats: {
                strength: this.strength,
                endurance: this.endurance,
                agility: this.agility,
                luck: this.luck
            },
            derived: {
                damage: this.damage,
                speed: this.speed,
                critChance: this.critChance
            },
            currency: {
                gold: this.gold,
                gems: this.gems,
                goblinParts: this.goblinParts
            },
            progress: {
                killCount: this.killCount,
                survivalTime: this.survivalTime
            }
        };
    }
}
