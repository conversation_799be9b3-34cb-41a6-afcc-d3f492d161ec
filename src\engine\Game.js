/**
 * Core Game Engine
 * Manages the main game loop, PIXI application, and core systems
 */

import * as PIXI from 'pixi.js';
import { SceneManager } from './SceneManager.js';
import { InputManager } from './InputManager.js';
import { AudioManager } from '../audio/AudioManager.js';

export class Game {
    constructor(config) {
        this.config = config;
        this.app = null;
        this.sceneManager = null;
        this.inputManager = null;
        this.audioManager = null;
        
        // Game state
        this.isRunning = false;
        this.isPaused = false;

        // Timing and game loop
        this.deltaTime = 0;
        this.lastTime = 0;
        this.accumulator = 0;
        this.fixedTimeStep = 1/60; // 60 FPS fixed timestep
        this.maxFrameTime = 0.25; // Cap frame time to prevent spiral of death

        // Performance tracking
        this.fps = 60;
        this.frameCount = 0;
        this.fpsUpdateTime = 0;
        this.renderTime = 0;
        this.updateTime = 0;
        
        this.init();
    }
    
    init() {
        // Create PIXI application
        this.app = new PIXI.Application({
            width: this.config.width,
            height: this.config.height,
            backgroundColor: this.config.backgroundColor,
            antialias: this.config.antialias,
            resolution: this.config.resolution
        });
        
        // Add canvas to DOM
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.appendChild(this.app.view);
        } else {
            document.body.appendChild(this.app.view);
        }
        
        // Initialize managers
        this.sceneManager = new SceneManager(this);
        this.inputManager = new InputManager(this);
        this.audioManager = new AudioManager();
        
        // Set up game loop
        this.app.ticker.add(this.gameLoop.bind(this));
        
        // Handle window events
        this.setupWindowEvents();
        
        console.log('🎮 Game engine initialized');
    }
    
    setupWindowEvents() {
        // Handle window focus/blur for pausing
        window.addEventListener('blur', () => {
            this.pause();
        });
        
        window.addEventListener('focus', () => {
            this.resume();
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    handleResize() {
        // Keep game centered and maintain aspect ratio
        const container = document.getElementById('game-container');
        if (container) {
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const gameAspect = this.config.width / this.config.height;
            const windowAspect = windowWidth / windowHeight;
            
            let scale = 1;
            if (windowAspect > gameAspect) {
                scale = windowHeight / this.config.height;
            } else {
                scale = windowWidth / this.config.width;
            }
            
            // Limit scale to prevent too small/large display
            scale = Math.max(0.5, Math.min(2, scale));
            
            this.app.view.style.transform = `scale(${scale})`;
        }
    }
    
    gameLoop(delta) {
        if (!this.isRunning || this.isPaused) return;

        const startTime = performance.now();

        // Calculate frame time
        const currentTime = performance.now();
        let frameTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;

        // Cap frame time to prevent spiral of death
        frameTime = Math.min(frameTime, this.maxFrameTime);

        // Fixed timestep game loop with accumulator
        this.accumulator += frameTime;

        const updateStartTime = performance.now();

        // Update with fixed timestep
        while (this.accumulator >= this.fixedTimeStep) {
            // Update current scene with fixed timestep
            if (this.sceneManager.currentScene) {
                this.sceneManager.currentScene.update(this.fixedTimeStep);
            }

            // Update input manager
            this.inputManager.update();

            this.accumulator -= this.fixedTimeStep;
        }

        this.updateTime = performance.now() - updateStartTime;

        // Calculate interpolation factor for smooth rendering
        const interpolation = this.accumulator / this.fixedTimeStep;

        const renderStartTime = performance.now();

        // Render with interpolation
        if (this.sceneManager.currentScene && this.sceneManager.currentScene.render) {
            this.sceneManager.currentScene.render(interpolation);
        }

        this.renderTime = performance.now() - renderStartTime;

        // Update performance metrics
        this.updateFPS();
        this.updatePerformanceMetrics(performance.now() - startTime);
    }
    
    updateFPS() {
        this.frameCount++;
        this.fpsUpdateTime += this.fixedTimeStep;

        if (this.fpsUpdateTime >= 1.0) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.fpsUpdateTime = 0;
        }
    }

    updatePerformanceMetrics(totalFrameTime) {
        // Store performance data for debugging
        this.performanceData = {
            fps: this.fps,
            frameTime: totalFrameTime,
            updateTime: this.updateTime,
            renderTime: this.renderTime,
            memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0
        };

        // Log performance warnings
        if (totalFrameTime > 16.67) { // More than 60 FPS
            console.warn(`⚠️ Frame time exceeded 16.67ms: ${totalFrameTime.toFixed(2)}ms`);
        }
    }
    
    async start(initialScene) {
        this.isRunning = true;
        this.lastTime = performance.now();
        
        // Start the initial scene
        if (initialScene) {
            await this.sceneManager.switchTo(initialScene);
        }
        
        // Initial resize
        this.handleResize();
        
        console.log('🚀 Game started');
    }
    
    pause() {
        this.isPaused = true;
        this.audioManager.pauseAll();
        console.log('⏸️ Game paused');
    }
    
    resume() {
        this.isPaused = false;
        this.lastTime = performance.now();
        this.audioManager.resumeAll();
        console.log('▶️ Game resumed');
    }
    
    stop() {
        this.isRunning = false;
        this.app.ticker.stop();
        this.audioManager.stopAll();
        console.log('⏹️ Game stopped');
    }
    
    destroy() {
        this.stop();
        this.sceneManager.destroy();
        this.inputManager.destroy();
        this.audioManager.destroy();
        this.app.destroy(true);
        console.log('💥 Game destroyed');
    }
    
    // Utility methods
    getWidth() {
        return this.config.width;
    }
    
    getHeight() {
        return this.config.height;
    }
    
    getFPS() {
        return this.fps;
    }
    
    getDeltaTime() {
        return this.fixedTimeStep;
    }

    getFixedTimeStep() {
        return this.fixedTimeStep;
    }

    getPerformanceData() {
        return this.performanceData || {};
    }

    // Debug method to display performance info
    showPerformanceInfo() {
        const data = this.getPerformanceData();
        console.log('🔧 Performance Info:', {
            FPS: data.fps,
            'Frame Time': `${data.frameTime?.toFixed(2)}ms`,
            'Update Time': `${data.updateTime?.toFixed(2)}ms`,
            'Render Time': `${data.renderTime?.toFixed(2)}ms`,
            'Memory Usage': `${(data.memoryUsage / 1024 / 1024).toFixed(2)}MB`
        });
    }
}
