/**
 * Main Menu Scene
 * Displays the game's main menu with D&D themed styling
 */

import * as PIXI from 'pixi.js';
import { Scene } from '../engine/Scene.js';

export class MenuScene extends Scene {
    constructor(game) {
        super(game);
        this.menuItems = [];
        this.selectedIndex = 0;
        this.titleText = null;
        this.subtitleText = null;
    }
    
    async enter(data = null) {
        await super.enter(data);
        this.createMenu();
        this.setupInput();
    }
    
    createMenu() {
        // Create background
        this.createBackground();
        
        // Create title
        this.createTitle();
        
        // Create menu items
        this.createMenuItems();
        
        // Create footer text
        this.createFooter();
    }
    
    createBackground() {
        // Dark forest background
        const bg = new PIXI.Graphics();
        bg.beginFill(0x1a2f0a); // Dark green
        bg.drawRect(0, 0, this.game.getWidth(), this.game.getHeight());
        bg.endFill();
        this.container.addChild(bg);
        
        // Add some decorative elements
        this.createDecorations();
    }
    
    createDecorations() {
        // Create some simple "trees" or decorative elements
        const decorations = new PIXI.Graphics();
        
        // Draw some simple tree shapes
        for (let i = 0; i < 8; i++) {
            const x = (i * this.game.getWidth() / 8) + Math.random() * 50;
            const y = this.game.getHeight() - 100 + Math.random() * 50;
            
            // Tree trunk
            decorations.beginFill(0x4a3c1a);
            decorations.drawRect(x - 5, y, 10, 40);
            decorations.endFill();
            
            // Tree top
            decorations.beginFill(0x2d5016);
            decorations.drawCircle(x, y - 10, 20 + Math.random() * 10);
            decorations.endFill();
        }
        
        this.container.addChild(decorations);
    }
    
    createTitle() {
        // Main title
        this.titleText = new PIXI.Text('GOBLIN SURVIVORS', {
            fontFamily: 'Courier New, monospace',
            fontSize: 52,
            fill: 0xFFD700, // Gold
            stroke: 0x8B4513, // Brown
            strokeThickness: 4,
            align: 'center'
        });

        this.titleText.anchor.set(0.5);
        this.titleText.x = this.game.getWidth() / 2;
        this.titleText.y = 100;
        this.container.addChild(this.titleText);

        // Subtitle
        this.subtitleText = new PIXI.Text('A D&D Horde Survival Adventure', {
            fontFamily: 'Courier New, monospace',
            fontSize: 20,
            fill: 0xDDDDDD,
            align: 'center',
            stroke: 0x000000,
            strokeThickness: 1
        });

        this.subtitleText.anchor.set(0.5);
        this.subtitleText.x = this.game.getWidth() / 2;
        this.subtitleText.y = 170;
        this.container.addChild(this.subtitleText);
    }
    
    createMenuItems() {
        const menuOptions = [
            { text: 'New Adventure', action: 'newGame' },
            { text: 'Continue Quest', action: 'continue' },
            { text: 'Visit Town', action: 'town' },
            { text: 'Settings', action: 'settings' },
            { text: 'Quit Game', action: 'quit' }
        ];

        const startY = 300;
        const spacing = 70;

        menuOptions.forEach((option, index) => {
            const menuItem = this.createMenuItem(option.text, option.action, index);
            menuItem.container.y = startY + (index * spacing);
            this.menuItems.push(menuItem);
            this.container.addChild(menuItem.container);
        });

        this.updateSelection();
    }
    
    createMenuItem(text, action, index) {
        const container = new PIXI.Container();

        // Background for selection
        const bg = new PIXI.Graphics();
        bg.beginFill(0x8B4513, 0.5);
        bg.drawRoundedRect(-160, -25, 320, 50, 8);
        bg.endFill();
        bg.visible = false;
        container.addChild(bg);

        // Text
        const textObj = new PIXI.Text(text, {
            fontFamily: 'Courier New, monospace',
            fontSize: 28,
            fill: 0xFFFFFF,
            align: 'center',
            stroke: 0x000000,
            strokeThickness: 2
        });

        textObj.anchor.set(0.5);
        container.addChild(textObj);

        // Position
        container.x = this.game.getWidth() / 2;

        // Store references
        container.bg = bg;
        container.textObj = textObj;
        container.action = action;
        container.index = index;

        // Make interactive
        container.interactive = true;
        container.buttonMode = true;

        container.on('pointerover', () => {
            this.selectedIndex = index;
            this.updateSelection();
        });

        container.on('pointerdown', () => {
            this.selectMenuItem();
        });

        return { container, bg, textObj, action };
    }
    
    createFooter() {
        const footerText = new PIXI.Text('Use ARROW KEYS to navigate, ENTER to select', {
            fontFamily: 'Courier New, monospace',
            fontSize: 16,
            fill: 0xAAAAAA,
            align: 'center',
            stroke: 0x000000,
            strokeThickness: 1
        });

        footerText.anchor.set(0.5);
        footerText.x = this.game.getWidth() / 2;
        footerText.y = this.game.getHeight() - 60;
        this.container.addChild(footerText);
    }
    
    setupInput() {
        // Input will be handled in update method
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        this.handleInput();
        this.updateAnimations(deltaTime);
    }
    
    handleInput() {
        const input = this.game.inputManager;
        
        // Navigate menu
        if (input.isKeyPressed('ArrowUp') || input.isKeyPressed('KeyW')) {
            this.selectedIndex = Math.max(0, this.selectedIndex - 1);
            this.updateSelection();
        }
        
        if (input.isKeyPressed('ArrowDown') || input.isKeyPressed('KeyS')) {
            this.selectedIndex = Math.min(this.menuItems.length - 1, this.selectedIndex + 1);
            this.updateSelection();
        }
        
        // Select item
        if (input.isKeyPressed('Enter') || input.isKeyPressed('Space')) {
            this.selectMenuItem();
        }
    }
    
    updateSelection() {
        this.menuItems.forEach((item, index) => {
            const isSelected = index === this.selectedIndex;
            item.bg.visible = isSelected;
            item.textObj.style.fill = isSelected ? 0xFFD700 : 0xFFFFFF;

            // Add slight scale effect for selected item
            const scale = isSelected ? 1.1 : 1.0;
            item.container.scale.set(scale);
        });
    }
    
    selectMenuItem() {
        const selectedItem = this.menuItems[this.selectedIndex];
        if (!selectedItem) return;
        
        console.log(`🎯 Selected menu item: ${selectedItem.action}`);
        
        switch (selectedItem.action) {
            case 'newGame':
                this.game.sceneManager.switchTo('game', { newGame: true });
                break;
            case 'continue':
                // TODO: Load saved game
                this.game.sceneManager.switchTo('game', { continue: true });
                break;
            case 'town':
                this.game.sceneManager.switchTo('town');
                break;
            case 'settings':
                // TODO: Implement settings
                console.log('⚙️ Settings not implemented yet');
                break;
            case 'quit':
                if (confirm('Are you sure you want to quit?')) {
                    window.close();
                }
                break;
        }
    }
    
    updateAnimations(deltaTime) {
        // Simple title animation
        if (this.titleText) {
            this.titleText.scale.x = 1 + Math.sin(Date.now() * 0.002) * 0.05;
            this.titleText.scale.y = 1 + Math.sin(Date.now() * 0.002) * 0.05;
        }
    }
}
