/**
 * Base Scene Class
 * All game scenes inherit from this class
 */

import * as PIXI from 'pixi.js';
import { World } from './ECS.js';
import { Camera } from './Camera.js';

export class Scene {
    constructor(game) {
        this.game = game;
        this.container = new PIXI.Container();
        this.isActive = false;

        // ECS World
        this.world = new World();

        // Camera
        this.camera = new Camera(game);
        this.container.addChild(this.camera.container);

        // Legacy support for non-ECS entities
        this.entities = [];
        this.systems = [];
    }
    
    // Called when entering the scene
    async enter(data = null) {
        this.isActive = true;
        console.log(`🎬 Entering scene: ${this.constructor.name}`);
    }
    
    // Called when exiting the scene
    async exit() {
        this.isActive = false;
        console.log(`🚪 Exiting scene: ${this.constructor.name}`);
    }
    
    // Called every frame
    update(deltaTime) {
        if (!this.isActive) return;

        // Update camera
        this.camera.update(deltaTime);

        // Update ECS world
        this.world.update(deltaTime);

        // Update legacy systems
        for (const system of this.systems) {
            if (system.update) {
                system.update(deltaTime);
            }
        }

        // Update legacy entities
        for (const entity of this.entities) {
            if (entity.update) {
                entity.update(deltaTime);
            }
        }
    }

    // Called for rendering with interpolation
    render(interpolation = 0) {
        if (!this.isActive) return;

        // Render ECS world
        this.world.render(interpolation);
    }
    
    // Add entity to scene
    addEntity(entity) {
        this.entities.push(entity);
        if (entity.sprite) {
            this.container.addChild(entity.sprite);
        }
    }
    
    // Remove entity from scene
    removeEntity(entity) {
        const index = this.entities.indexOf(entity);
        if (index !== -1) {
            this.entities.splice(index, 1);
            if (entity.sprite && entity.sprite.parent) {
                entity.sprite.parent.removeChild(entity.sprite);
            }
        }
    }
    
    // Add system to scene
    addSystem(system) {
        this.systems.push(system);
    }
    
    // Remove system from scene
    removeSystem(system) {
        const index = this.systems.indexOf(system);
        if (index !== -1) {
            this.systems.splice(index, 1);
        }
    }
    
    // Get entities by type or filter
    getEntities(filter = null) {
        if (!filter) return this.entities;
        
        if (typeof filter === 'string') {
            return this.entities.filter(entity => entity.type === filter);
        }
        
        if (typeof filter === 'function') {
            return this.entities.filter(filter);
        }
        
        return this.entities;
    }
    
    // Clear all entities
    clearEntities() {
        for (const entity of this.entities) {
            if (entity.sprite && entity.sprite.parent) {
                entity.sprite.parent.removeChild(entity.sprite);
            }
            if (entity.destroy) {
                entity.destroy();
            }
        }
        this.entities = [];
    }
    
    // ECS helper methods
    createEntity() {
        return this.world.createEntity();
    }

    addSystem(system) {
        return this.world.addSystem(system);
    }

    getSystem(SystemClass) {
        return this.world.getSystem(SystemClass);
    }

    getEntitiesWith(...ComponentClasses) {
        return this.world.getEntitiesWith(...ComponentClasses);
    }

    // Cleanup when scene is destroyed
    destroy() {
        this.clearEntities();
        this.systems = [];

        // Destroy ECS world
        this.world.clear();

        // Destroy camera
        this.camera.destroy();

        if (this.container.parent) {
            this.container.parent.removeChild(this.container);
        }
        this.container.destroy();

        console.log(`💥 Scene destroyed: ${this.constructor.name}`);
    }
}
