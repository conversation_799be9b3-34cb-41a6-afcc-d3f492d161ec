/**
 * Render System
 * Handles rendering of sprites
 */

import { System } from '../engine/ECS.js';
import { Transform } from '../components/Transform.js';
import { Sprite } from '../components/Sprite.js';

export class RenderSystem extends System {
    constructor(container) {
        super();
        this.requiredComponents = [Transform, Sprite];
        this.container = container; // PIXI container to render to
    }
    
    getRequiredComponents() {
        return this.requiredComponents;
    }
    
    onEntityAdded(entity) {
        const sprite = entity.getComponent(Sprite);
        if (sprite && sprite.sprite) {
            this.container.addChild(sprite.sprite);
        }
    }
    
    onEntityRemoved(entity) {
        const sprite = entity.getComponent(Sprite);
        if (sprite && sprite.sprite && sprite.sprite.parent) {
            sprite.sprite.parent.removeChild(sprite.sprite);
        }
    }
    
    updateEntity(entity, deltaTime) {
        // Update sprite properties from transform
        const transform = entity.getComponent(Transform);
        const sprite = entity.getComponent(Sprite);
        
        if (transform && sprite) {
            sprite.updateFromTransform(transform);
        }
    }
    
    render(interpolation = 0) {
        // Update sprite positions with interpolation for smooth rendering
        for (const entity of this.entities) {
            const transform = entity.getComponent(Transform);
            const sprite = entity.getComponent(Sprite);
            
            if (transform && sprite) {
                sprite.updateFromTransform(transform, interpolation);
            }
        }
        
        // Sort sprites by z-index
        this.container.children.sort((a, b) => {
            const aZIndex = a.zIndex || 0;
            const bZIndex = b.zIndex || 0;
            return aZIndex - bZIndex;
        });
    }
    
    destroy() {
        // Remove all sprites from container
        for (const entity of this.entities) {
            this.onEntityRemoved(entity);
        }
        super.destroy();
    }
}
