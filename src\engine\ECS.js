/**
 * Entity Component System (ECS) Architecture
 * Core ECS implementation for managing game objects
 */

// Entity - Just an ID
export class Entity {
    static nextId = 1;
    
    constructor() {
        this.id = Entity.nextId++;
        this.components = new Map();
        this.active = true;
    }
    
    addComponent(component) {
        this.components.set(component.constructor.name, component);
        component.entity = this;
        return this;
    }
    
    removeComponent(ComponentClass) {
        const componentName = ComponentClass.name;
        const component = this.components.get(componentName);
        if (component) {
            this.components.delete(componentName);
            component.entity = null;
        }
        return this;
    }
    
    getComponent(ComponentClass) {
        return this.components.get(ComponentClass.name);
    }
    
    hasComponent(ComponentClass) {
        return this.components.has(ComponentClass.name);
    }
    
    hasComponents(...ComponentClasses) {
        return ComponentClasses.every(ComponentClass => 
            this.components.has(ComponentClass.name)
        );
    }
    
    destroy() {
        this.active = false;
        this.components.clear();
    }
}

// Base Component class
export class Component {
    constructor() {
        this.entity = null;
    }
    
    destroy() {
        this.entity = null;
    }
}

// Base System class
export class System {
    constructor() {
        this.entities = [];
        this.requiredComponents = [];
        this.active = true;
    }
    
    // Override this to define required components
    getRequiredComponents() {
        return this.requiredComponents;
    }
    
    // Check if entity matches system requirements
    matches(entity) {
        const required = this.getRequiredComponents();
        return required.length > 0 && entity.hasComponents(...required);
    }
    
    // Add entity to system
    addEntity(entity) {
        if (!this.entities.includes(entity)) {
            this.entities.push(entity);
            this.onEntityAdded(entity);
        }
    }
    
    // Remove entity from system
    removeEntity(entity) {
        const index = this.entities.indexOf(entity);
        if (index !== -1) {
            this.entities.splice(index, 1);
            this.onEntityRemoved(entity);
        }
    }
    
    // Override these methods in derived systems
    onEntityAdded(entity) {}
    onEntityRemoved(entity) {}
    
    // Main update method - override in derived systems
    update(deltaTime) {
        if (!this.active) return;
        
        // Remove inactive entities
        this.entities = this.entities.filter(entity => entity.active);
        
        // Update each entity
        for (const entity of this.entities) {
            this.updateEntity(entity, deltaTime);
        }
    }
    
    // Override this to update individual entities
    updateEntity(entity, deltaTime) {}
    
    // Render method for systems that need to render
    render(interpolation = 0) {}
    
    destroy() {
        this.entities = [];
        this.active = false;
    }
}

// ECS World - manages entities, components, and systems
export class World {
    constructor() {
        this.entities = [];
        this.systems = [];
        this.entitiesToAdd = [];
        this.entitiesToRemove = [];
    }
    
    // Create a new entity
    createEntity() {
        const entity = new Entity();
        this.entitiesToAdd.push(entity);
        return entity;
    }
    
    // Remove an entity
    removeEntity(entity) {
        if (!this.entitiesToRemove.includes(entity)) {
            this.entitiesToRemove.push(entity);
        }
    }
    
    // Add a system
    addSystem(system) {
        this.systems.push(system);
        
        // Add existing entities that match the system
        for (const entity of this.entities) {
            if (system.matches(entity)) {
                system.addEntity(entity);
            }
        }
        
        return system;
    }
    
    // Remove a system
    removeSystem(system) {
        const index = this.systems.indexOf(system);
        if (index !== -1) {
            this.systems.splice(index, 1);
            system.destroy();
        }
    }
    
    // Get system by type
    getSystem(SystemClass) {
        return this.systems.find(system => system instanceof SystemClass);
    }
    
    // Get entities with specific components
    getEntitiesWith(...ComponentClasses) {
        return this.entities.filter(entity => 
            entity.active && entity.hasComponents(...ComponentClasses)
        );
    }
    
    // Update all systems
    update(deltaTime) {
        // Process entity additions and removals
        this.processEntityChanges();
        
        // Update all systems
        for (const system of this.systems) {
            system.update(deltaTime);
        }
    }
    
    // Render all systems
    render(interpolation = 0) {
        for (const system of this.systems) {
            if (system.render) {
                system.render(interpolation);
            }
        }
    }
    
    // Process pending entity additions and removals
    processEntityChanges() {
        // Add new entities
        for (const entity of this.entitiesToAdd) {
            this.entities.push(entity);
            
            // Add to matching systems
            for (const system of this.systems) {
                if (system.matches(entity)) {
                    system.addEntity(entity);
                }
            }
        }
        this.entitiesToAdd = [];
        
        // Remove entities
        for (const entity of this.entitiesToRemove) {
            const index = this.entities.indexOf(entity);
            if (index !== -1) {
                this.entities.splice(index, 1);
                
                // Remove from all systems
                for (const system of this.systems) {
                    system.removeEntity(entity);
                }
                
                entity.destroy();
            }
        }
        this.entitiesToRemove = [];
    }
    
    // Clear all entities and systems
    clear() {
        for (const entity of this.entities) {
            entity.destroy();
        }
        
        for (const system of this.systems) {
            system.destroy();
        }
        
        this.entities = [];
        this.systems = [];
        this.entitiesToAdd = [];
        this.entitiesToRemove = [];
    }
    
    // Get statistics
    getStats() {
        return {
            entities: this.entities.length,
            systems: this.systems.length,
            activeEntities: this.entities.filter(e => e.active).length
        };
    }
}
