/**
 * Transform Component
 * Handles position, rotation, and scale
 */

import { Component } from '../engine/ECS.js';

export class Transform extends Component {
    constructor(x = 0, y = 0, rotation = 0, scaleX = 1, scaleY = 1) {
        super();
        this.x = x;
        this.y = y;
        this.rotation = rotation;
        this.scaleX = scaleX;
        this.scaleY = scaleY;
        
        // Previous position for interpolation
        this.prevX = x;
        this.prevY = y;
        this.prevRotation = rotation;
    }
    
    setPosition(x, y) {
        this.prevX = this.x;
        this.prevY = this.y;
        this.x = x;
        this.y = y;
    }
    
    translate(dx, dy) {
        this.prevX = this.x;
        this.prevY = this.y;
        this.x += dx;
        this.y += dy;
    }
    
    setRotation(rotation) {
        this.prevRotation = this.rotation;
        this.rotation = rotation;
    }
    
    rotate(deltaRotation) {
        this.prevRotation = this.rotation;
        this.rotation += deltaRotation;
    }
    
    setScale(scaleX, scaleY = scaleX) {
        this.scaleX = scaleX;
        this.scaleY = scaleY;
    }
    
    // Get interpolated position for smooth rendering
    getInterpolatedPosition(interpolation) {
        return {
            x: this.prevX + (this.x - this.prevX) * interpolation,
            y: this.prevY + (this.y - this.prevY) * interpolation
        };
    }
    
    getInterpolatedRotation(interpolation) {
        return this.prevRotation + (this.rotation - this.prevRotation) * interpolation;
    }
    
    // Distance calculation
    distanceTo(other) {
        const dx = this.x - other.x;
        const dy = this.y - other.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    // Direction calculation
    directionTo(other) {
        const dx = other.x - this.x;
        const dy = other.y - this.y;
        return Math.atan2(dy, dx);
    }
    
    // Copy current position to previous for next frame
    updatePrevious() {
        this.prevX = this.x;
        this.prevY = this.y;
        this.prevRotation = this.rotation;
    }
}
