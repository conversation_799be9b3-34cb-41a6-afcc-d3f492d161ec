/**
 * Health Component
 * Handles entity health, damage, and death
 */

import { Component } from '../engine/ECS.js';

export class Health extends Component {
    constructor(maxHealth = 100, currentHealth = null) {
        super();
        this.maxHealth = maxHealth;
        this.currentHealth = currentHealth !== null ? currentHealth : maxHealth;
        this.isDead = false;
        
        // Damage resistance/armor
        this.armor = 0;
        this.damageReduction = 0; // Percentage (0-1)
        
        // Regeneration
        this.regeneration = 0; // HP per second
        this.regenerationDelay = 0; // Delay after taking damage
        this.timeSinceLastDamage = 0;
        
        // Invincibility frames
        this.invincibilityDuration = 0;
        this.invincibilityTimer = 0;
        
        // Events
        this.onDamage = null;
        this.onHeal = null;
        this.onDeath = null;
    }
    
    // Take damage
    takeDamage(amount, damageType = 'physical') {
        if (this.isDead || this.invincibilityTimer > 0) {
            return 0; // No damage taken
        }
        
        // Calculate actual damage after armor/resistance
        let actualDamage = amount;
        
        // Apply armor (flat reduction)
        actualDamage = Math.max(0, actualDamage - this.armor);
        
        // Apply damage reduction (percentage)
        actualDamage *= (1 - this.damageReduction);
        
        // Round damage
        actualDamage = Math.floor(actualDamage);
        
        if (actualDamage > 0) {
            this.currentHealth = Math.max(0, this.currentHealth - actualDamage);
            this.timeSinceLastDamage = 0;
            
            // Set invincibility frames
            if (this.invincibilityDuration > 0) {
                this.invincibilityTimer = this.invincibilityDuration;
            }
            
            // Trigger damage event
            if (this.onDamage) {
                this.onDamage(actualDamage, damageType);
            }
            
            // Check for death
            if (this.currentHealth <= 0 && !this.isDead) {
                this.die();
            }
        }
        
        return actualDamage;
    }
    
    // Heal
    heal(amount) {
        if (this.isDead) return 0;
        
        const oldHealth = this.currentHealth;
        this.currentHealth = Math.min(this.maxHealth, this.currentHealth + amount);
        const actualHealing = this.currentHealth - oldHealth;
        
        if (actualHealing > 0 && this.onHeal) {
            this.onHeal(actualHealing);
        }
        
        return actualHealing;
    }
    
    // Set health to maximum
    fullHeal() {
        return this.heal(this.maxHealth - this.currentHealth);
    }
    
    // Kill entity
    die() {
        if (this.isDead) return;
        
        this.isDead = true;
        this.currentHealth = 0;
        
        if (this.onDeath) {
            this.onDeath();
        }
    }
    
    // Revive entity
    revive(health = null) {
        this.isDead = false;
        this.currentHealth = health !== null ? health : this.maxHealth;
        this.invincibilityTimer = 0;
    }
    
    // Update (called by health system)
    update(deltaTime) {
        // Update invincibility timer
        if (this.invincibilityTimer > 0) {
            this.invincibilityTimer -= deltaTime;
        }
        
        // Update regeneration
        if (!this.isDead && this.regeneration > 0) {
            this.timeSinceLastDamage += deltaTime;
            
            if (this.timeSinceLastDamage >= this.regenerationDelay) {
                this.heal(this.regeneration * deltaTime);
            }
        }
    }
    
    // Getters
    getHealthPercentage() {
        return this.maxHealth > 0 ? this.currentHealth / this.maxHealth : 0;
    }
    
    isAlive() {
        return !this.isDead && this.currentHealth > 0;
    }
    
    isInvincible() {
        return this.invincibilityTimer > 0;
    }
    
    isCriticalHealth(threshold = 0.25) {
        return this.getHealthPercentage() <= threshold;
    }
    
    // Setters
    setMaxHealth(maxHealth) {
        const ratio = this.getHealthPercentage();
        this.maxHealth = maxHealth;
        this.currentHealth = Math.floor(maxHealth * ratio);
    }
    
    setArmor(armor) {
        this.armor = Math.max(0, armor);
    }
    
    setDamageReduction(reduction) {
        this.damageReduction = Math.max(0, Math.min(1, reduction));
    }
    
    setRegeneration(regen, delay = 3.0) {
        this.regeneration = Math.max(0, regen);
        this.regenerationDelay = Math.max(0, delay);
    }
    
    setInvincibilityDuration(duration) {
        this.invincibilityDuration = Math.max(0, duration);
    }
    
    // Event handlers
    setOnDamage(callback) {
        this.onDamage = callback;
    }
    
    setOnHeal(callback) {
        this.onHeal = callback;
    }
    
    setOnDeath(callback) {
        this.onDeath = callback;
    }
}
