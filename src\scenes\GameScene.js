/**
 * Main Game Scene
 * Where the goblin-slaying action happens
 */

import * as PIXI from 'pixi.js';
import { Scene } from '../engine/Scene.js';
import { PlayerFactory } from '../entities/PlayerFactory.js';
import { PlayerInputSystem } from '../systems/PlayerInputSystem.js';
import { MovementSystem } from '../systems/MovementSystem.js';
import { RenderSystem } from '../systems/RenderSystem.js';
import { Transform } from '../components/Transform.js';
import { Player } from '../components/Player.js';
import { Health } from '../components/Health.js';

export class GameScene extends Scene {
    constructor(game) {
        super(game);
        this.playerEntity = null;
        this.enemies = [];
        this.gameTime = 0;
        this.isGameActive = false;

        // Systems
        this.playerInputSystem = null;
        this.movementSystem = null;
        this.renderSystem = null;
    }
    
    async enter(data = null) {
        await super.enter(data);
        
        console.log('🎮 Starting goblin survival adventure!');
        
        if (data?.newGame) {
            this.startNewGame();
        } else if (data?.continue) {
            this.continueGame();
        } else {
            this.startNewGame();
        }
    }
    
    startNewGame() {
        console.log('🆕 Starting new game');
        this.setupGame();
        this.isGameActive = true;
    }
    
    continueGame() {
        console.log('📖 Continuing saved game');
        // TODO: Load saved game state
        this.setupGame();
        this.isGameActive = true;
    }
    
    setupGame() {
        this.createBackground();
        this.setupSystems();
        this.createPlayer();
        this.createHUD();
        this.gameTime = 0;
    }
    
    createBackground() {
        // Create a simple grass background
        const bg = new PIXI.Graphics();
        bg.beginFill(0x2d5016); // Forest green
        bg.drawRect(0, 0, this.game.getWidth(), this.game.getHeight());
        bg.endFill();
        this.container.addChild(bg);
        
        // Add some texture with simple shapes
        this.createEnvironmentDetails();
    }
    
    createEnvironmentDetails() {
        const details = new PIXI.Graphics();
        
        // Add some grass patches
        for (let i = 0; i < 50; i++) {
            const x = Math.random() * this.game.getWidth();
            const y = Math.random() * this.game.getHeight();
            const size = 2 + Math.random() * 3;
            
            details.beginFill(0x3a6b1a, 0.6);
            details.drawCircle(x, y, size);
            details.endFill();
        }
        
        // Add some rocks
        for (let i = 0; i < 20; i++) {
            const x = Math.random() * this.game.getWidth();
            const y = Math.random() * this.game.getHeight();
            const size = 3 + Math.random() * 5;
            
            details.beginFill(0x666666, 0.8);
            details.drawCircle(x, y, size);
            details.endFill();
        }
        
        this.container.addChild(details);
    }
    
    setupSystems() {
        // Create and add systems to the ECS world
        this.renderSystem = new RenderSystem(this.camera.container);
        this.movementSystem = new MovementSystem();
        this.playerInputSystem = new PlayerInputSystem(this.game);

        this.addSystem(this.renderSystem);
        this.addSystem(this.movementSystem);
        this.addSystem(this.playerInputSystem);

        console.log('⚙️ Game systems initialized');
    }

    createPlayer() {
        // Create player entity using ECS
        const startX = this.game.getWidth() / 2;
        const startY = this.game.getHeight() / 2;

        this.playerEntity = PlayerFactory.createPlayer(this.world, startX, startY);

        // Set camera to follow player
        this.camera.setTarget(this.playerEntity);

        console.log('🧙‍♂️ Player entity created at center of battlefield');
    }
    
    createHUD() {
        // Create HUD container
        this.hudContainer = new PIXI.Container();
        this.container.addChild(this.hudContainer);
        
        // Health bar background
        const healthBg = new PIXI.Graphics();
        healthBg.beginFill(0x8B0000);
        healthBg.drawRoundedRect(20, 20, 200, 20, 5);
        healthBg.endFill();
        this.hudContainer.addChild(healthBg);
        
        // Health bar
        this.healthBar = new PIXI.Graphics();
        this.hudContainer.addChild(this.healthBar);
        
        // XP bar background
        const xpBg = new PIXI.Graphics();
        xpBg.beginFill(0x4B0082);
        xpBg.drawRoundedRect(20, 50, 200, 15, 5);
        xpBg.endFill();
        this.hudContainer.addChild(xpBg);
        
        // XP bar
        this.xpBar = new PIXI.Graphics();
        this.hudContainer.addChild(this.xpBar);
        
        // Level text
        this.levelText = new PIXI.Text('Level 1', {
            fontFamily: 'Courier New, monospace',
            fontSize: 16,
            fill: 0xFFD700
        });
        this.levelText.x = 20;
        this.levelText.y = 75;
        this.hudContainer.addChild(this.levelText);
        
        // Timer text
        this.timerText = new PIXI.Text('00:00', {
            fontFamily: 'Courier New, monospace',
            fontSize: 18,
            fill: 0xFFFFFF
        });
        this.timerText.x = this.game.getWidth() - 100;
        this.timerText.y = 20;
        this.hudContainer.addChild(this.timerText);
        
        this.updateHUD();
    }
    
    updateHUD() {
        if (!this.playerEntity) return;

        const playerComponent = this.playerEntity.getComponent(Player);
        const healthComponent = this.playerEntity.getComponent(Health);

        if (!playerComponent || !healthComponent) return;

        // Update health bar
        this.healthBar.clear();
        const healthPercent = healthComponent.getHealthPercentage();
        this.healthBar.beginFill(0xFF0000);
        this.healthBar.drawRoundedRect(20, 20, 200 * healthPercent, 20, 5);
        this.healthBar.endFill();

        // Update XP bar
        this.xpBar.clear();
        const xpPercent = playerComponent.getExperiencePercentage();
        this.xpBar.beginFill(0x9370DB);
        this.xpBar.drawRoundedRect(20, 50, 200 * xpPercent, 15, 5);
        this.xpBar.endFill();

        // Update level text
        this.levelText.text = `Level ${playerComponent.level}`;

        // Update timer
        const minutes = Math.floor(this.gameTime / 60);
        const seconds = Math.floor(this.gameTime % 60);
        this.timerText.text = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        
        if (!this.isGameActive) return;
        
        this.gameTime += deltaTime;
        this.updatePlayer(deltaTime);
        this.updateHUD();
        this.handleInput();
    }
    
    updatePlayer(deltaTime) {
        if (!this.playerEntity) return;

        const playerComponent = this.playerEntity.getComponent(Player);
        if (playerComponent) {
            playerComponent.survivalTime += deltaTime;
        }
    }

    handleInput() {
        const input = this.game.inputManager;

        // Escape to pause/menu
        if (input.isKeyPressed('Escape')) {
            this.pauseGame();
        }

        // Debug: Add XP with X key
        if (input.isKeyPressed('KeyX')) {
            const playerComponent = this.playerEntity?.getComponent(Player);
            if (playerComponent) {
                playerComponent.addExperience(25);
                console.log(`🎲 Added XP! Level: ${playerComponent.level}, XP: ${playerComponent.experience}/${playerComponent.experienceToNext}`);
            }
        }
    }
    
    pauseGame() {
        this.isGameActive = false;
        console.log('⏸️ Game paused');
        
        // Show pause menu or return to main menu
        if (confirm('Return to main menu? (Progress will be lost)')) {
            this.game.sceneManager.switchTo('menu');
        } else {
            this.isGameActive = true;
        }
    }
    
    async exit() {
        await super.exit();
        console.log('🚪 Leaving the battlefield...');
    }
}
