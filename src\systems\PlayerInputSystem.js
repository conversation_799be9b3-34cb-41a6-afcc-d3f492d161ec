/**
 * Player Input System
 * Handles player movement and input
 */

import { System } from '../engine/ECS.js';
import { Transform } from '../components/Transform.js';
import { Velocity } from '../components/Velocity.js';
import { Player } from '../components/Player.js';

export class PlayerInputSystem extends System {
    constructor(game) {
        super();
        this.game = game;
        this.requiredComponents = [Transform, Velocity, Player];
    }
    
    getRequiredComponents() {
        return this.requiredComponents;
    }
    
    updateEntity(entity, deltaTime) {
        const transform = entity.getComponent(Transform);
        const velocity = entity.getComponent(Velocity);
        const player = entity.getComponent(Player);
        
        if (!transform || !velocity || !player) return;
        
        const input = this.game.inputManager;
        
        // Get movement input
        const movement = input.getMovementVector();
        
        // Apply movement
        if (movement.x !== 0 || movement.y !== 0) {
            velocity.setVelocity(
                movement.x * player.speed,
                movement.y * player.speed
            );
        } else {
            // Apply friction when not moving
            velocity.scale(0.8); // Quick stop
        }
        
        // Keep player in world bounds (basic implementation)
        this.constrainToWorldBounds(transform);
    }
    
    constrainToWorldBounds(transform) {
        const margin = 20;
        const worldWidth = 2000;
        const worldHeight = 2000;
        
        transform.x = Math.max(margin, Math.min(worldWidth - margin, transform.x));
        transform.y = Math.max(margin, Math.min(worldHeight - margin, transform.y));
    }
}
