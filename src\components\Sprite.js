/**
 * Sprite Component
 * Handles visual representation using PIXI sprites
 */

import { Component } from '../engine/ECS.js';
import * as PIXI from 'pixi.js';

export class Sprite extends Component {
    constructor(texture = null, tint = 0xFFFFFF) {
        super();
        
        if (texture) {
            this.sprite = new PIXI.Sprite(texture);
        } else {
            // Create a simple colored rectangle as default
            this.sprite = this.createDefaultSprite();
        }
        
        this.sprite.anchor.set(0.5); // Center anchor
        this.sprite.tint = tint;
        
        // Sprite properties
        this.visible = true;
        this.alpha = 1.0;
        this.zIndex = 0;
    }
    
    createDefaultSprite(width = 20, height = 20, color = 0xFF0000) {
        const graphics = new PIXI.Graphics();
        graphics.beginFill(color);
        graphics.drawRect(-width/2, -height/2, width, height);
        graphics.endFill();
        
        const texture = PIXI.RenderTexture.create({ width, height });
        const renderer = PIXI.autoDetectRenderer();
        renderer.render(graphics, { renderTexture: texture });
        
        return new PIXI.Sprite(texture);
    }
    
    setTexture(texture) {
        this.sprite.texture = texture;
    }
    
    setTint(tint) {
        this.sprite.tint = tint;
        return this;
    }
    
    setAlpha(alpha) {
        this.alpha = alpha;
        this.sprite.alpha = alpha;
        return this;
    }
    
    setVisible(visible) {
        this.visible = visible;
        this.sprite.visible = visible;
        return this;
    }
    
    setZIndex(zIndex) {
        this.zIndex = zIndex;
        this.sprite.zIndex = zIndex;
        return this;
    }
    
    // Animation support
    playAnimation(animationName) {
        // TODO: Implement animation system
        console.log(`Playing animation: ${animationName}`);
    }
    
    stopAnimation() {
        // TODO: Implement animation system
    }
    
    // Update sprite position from transform
    updateFromTransform(transform, interpolation = 0) {
        if (interpolation > 0) {
            const pos = transform.getInterpolatedPosition(interpolation);
            const rot = transform.getInterpolatedRotation(interpolation);
            
            this.sprite.x = pos.x;
            this.sprite.y = pos.y;
            this.sprite.rotation = rot;
        } else {
            this.sprite.x = transform.x;
            this.sprite.y = transform.y;
            this.sprite.rotation = transform.rotation;
        }
        
        this.sprite.scale.set(transform.scaleX, transform.scaleY);
        this.sprite.visible = this.visible;
        this.sprite.alpha = this.alpha;
    }
    
    // Get sprite bounds
    getBounds() {
        return this.sprite.getBounds();
    }
    
    destroy() {
        super.destroy();
        if (this.sprite && this.sprite.parent) {
            this.sprite.parent.removeChild(this.sprite);
        }
        if (this.sprite) {
            this.sprite.destroy();
        }
    }
}
