/**
 * Player Factory
 * Creates player entities with all necessary components
 */

import * as PIXI from 'pixi.js';
import { Transform } from '../components/Transform.js';
import { Velocity } from '../components/Velocity.js';
import { Sprite } from '../components/Sprite.js';
import { Health } from '../components/Health.js';
import { Player } from '../components/Player.js';

export class PlayerFactory {
    static createPlayer(world, x = 0, y = 0) {
        // Create player entity
        const player = world.createEntity();
        
        // Add Transform component
        const transform = new Transform(x, y);
        player.addComponent(transform);
        
        // Add Velocity component
        const velocity = new Velocity(0, 0, 200); // Max speed of 200
        velocity.friction = 8; // Quick stopping
        player.addComponent(velocity);
        
        // Add Health component
        const health = new Health(100);
        health.setRegeneration(2, 5); // 2 HP/sec after 5 seconds
        health.setInvincibilityDuration(0.5); // 0.5 seconds of invincibility after damage
        
        // Set up health events
        health.setOnDamage((damage) => {
            console.log(`🩸 Player took ${damage} damage!`);
            // TODO: Add screen shake, damage effects
        });
        
        health.setOnDeath(() => {
            console.log('💀 Player died!');
            // TODO: Handle player death
        });
        
        player.addComponent(health);
        
        // Add Player component (stats, level, etc.)
        const playerComponent = new Player();
        player.addComponent(playerComponent);
        
        // Add Sprite component
        const sprite = PlayerFactory.createPlayerSprite();
        player.addComponent(sprite);

        console.log('🧙‍♂️ Player entity created with components:', {
            transform: !!player.getComponent(Transform),
            velocity: !!player.getComponent(Velocity),
            health: !!player.getComponent(Health),
            playerComponent: !!player.getComponent(Player),
            sprite: !!player.getComponent(Sprite)
        });

        return player;
    }
    
    static createPlayerSprite() {
        // Create a simple player sprite using graphics
        const graphics = new PIXI.Graphics();

        // Body (blue circle)
        graphics.beginFill(0x4169E1); // Royal blue
        graphics.drawCircle(0, 0, 15);
        graphics.endFill();

        // Face (white eyes)
        graphics.beginFill(0xFFFFFF);
        graphics.drawCircle(-5, -5, 3);
        graphics.drawCircle(5, -5, 3);
        graphics.endFill();

        // Pupils (black dots)
        graphics.beginFill(0x000000);
        graphics.drawCircle(-5, -5, 1);
        graphics.drawCircle(5, -5, 1);
        graphics.endFill();

        // Sword (simple line)
        graphics.lineStyle(3, 0xC0C0C0);
        graphics.moveTo(12, -8);
        graphics.lineTo(20, -16);
        graphics.lineStyle(0);

        // Sword hilt
        graphics.beginFill(0x8B4513);
        graphics.drawRect(10, -10, 4, 6);
        graphics.endFill();

        // Create sprite component with the graphics directly
        const sprite = new Sprite();
        sprite.sprite = graphics;

        console.log('🎨 Player sprite created');
        return sprite;
    }
    
    static createSimplePlayerSprite() {
        // Fallback: create a simple colored rectangle
        const graphics = new PIXI.Graphics();
        graphics.beginFill(0x4169E1);
        graphics.drawRect(-10, -10, 20, 20);
        graphics.endFill();
        
        const sprite = new Sprite();
        sprite.sprite = graphics;
        
        return sprite;
    }
}
