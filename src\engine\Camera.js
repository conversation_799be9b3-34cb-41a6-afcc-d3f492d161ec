/**
 * Camera System
 * Handles 2D camera movement, following, and screen boundaries
 */

import * as PIXI from 'pixi.js';
import { Transform } from '../components/Transform.js';

export class Camera {
    constructor(game, worldWidth = 2000, worldHeight = 2000) {
        this.game = game;
        this.container = new PIXI.Container();
        
        // Camera position
        this.x = 0;
        this.y = 0;
        this.targetX = 0;
        this.targetY = 0;
        
        // Camera properties
        this.zoom = 1.0;
        this.targetZoom = 1.0;
        this.minZoom = 0.5;
        this.maxZoom = 3.0;
        
        // Following
        this.target = null;
        this.followSpeed = 5.0;
        this.followOffset = { x: 0, y: 0 };
        
        // Smoothing
        this.smoothing = true;
        this.smoothFactor = 0.1;
        
        // World boundaries
        this.worldWidth = worldWidth;
        this.worldHeight = worldHeight;
        this.constrainToWorld = true;
        
        // Screen shake
        this.shakeIntensity = 0;
        this.shakeDuration = 0;
        this.shakeX = 0;
        this.shakeY = 0;
        
        // Viewport
        this.viewportWidth = game.getWidth();
        this.viewportHeight = game.getHeight();
        
        console.log('📷 Camera initialized');
    }
    
    // Set camera position
    setPosition(x, y) {
        this.x = x;
        this.y = y;
        this.targetX = x;
        this.targetY = y;
        this.updateTransform();
    }
    
    // Move camera
    move(dx, dy) {
        this.setPosition(this.x + dx, this.y + dy);
    }
    
    // Set camera target to follow
    setTarget(target) {
        this.target = target;
    }
    
    // Set follow offset
    setFollowOffset(x, y) {
        this.followOffset.x = x;
        this.followOffset.y = y;
    }
    
    // Set zoom level
    setZoom(zoom) {
        this.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, zoom));
        this.targetZoom = this.zoom;
        this.updateTransform();
    }
    
    // Zoom to target
    zoomTo(zoom, smooth = true) {
        this.targetZoom = Math.max(this.minZoom, Math.min(this.maxZoom, zoom));
        if (!smooth) {
            this.zoom = this.targetZoom;
            this.updateTransform();
        }
    }
    
    // Screen shake effect
    shake(intensity, duration) {
        this.shakeIntensity = intensity;
        this.shakeDuration = duration;
    }
    
    // Update camera
    update(deltaTime) {
        // Update following
        if (this.target) {
            this.updateFollowing(deltaTime);
        }
        
        // Update zoom smoothing
        if (this.smoothing && Math.abs(this.zoom - this.targetZoom) > 0.01) {
            this.zoom += (this.targetZoom - this.zoom) * this.smoothFactor;
        }
        
        // Update position smoothing
        if (this.smoothing) {
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            
            if (Math.abs(dx) > 0.1 || Math.abs(dy) > 0.1) {
                this.x += dx * this.smoothFactor;
                this.y += dy * this.smoothFactor;
            }
        } else {
            this.x = this.targetX;
            this.y = this.targetY;
        }
        
        // Update screen shake
        this.updateShake(deltaTime);
        
        // Constrain to world boundaries
        if (this.constrainToWorld) {
            this.constrainToWorldBounds();
        }
        
        // Update transform
        this.updateTransform();
    }
    
    updateFollowing(deltaTime) {
        if (!this.target) return;

        // Get target position (could be Transform component or simple object)
        let targetX, targetY;

        if (this.target.getComponent) {
            // ECS entity with Transform component
            const transform = this.target.getComponent(Transform);
            if (transform) {
                targetX = transform.x;
                targetY = transform.y;
            }
        } else if (this.target.x !== undefined && this.target.y !== undefined) {
            // Simple object with x, y properties
            targetX = this.target.x;
            targetY = this.target.y;
        }

        if (targetX !== undefined && targetY !== undefined) {
            this.targetX = targetX + this.followOffset.x;
            this.targetY = targetY + this.followOffset.y;
        }
    }
    
    updateShake(deltaTime) {
        if (this.shakeDuration > 0) {
            this.shakeDuration -= deltaTime;
            
            // Generate random shake offset
            const angle = Math.random() * Math.PI * 2;
            this.shakeX = Math.cos(angle) * this.shakeIntensity;
            this.shakeY = Math.sin(angle) * this.shakeIntensity;
            
            // Reduce intensity over time
            this.shakeIntensity *= 0.95;
            
            if (this.shakeDuration <= 0) {
                this.shakeX = 0;
                this.shakeY = 0;
                this.shakeIntensity = 0;
            }
        }
    }
    
    constrainToWorldBounds() {
        const halfViewWidth = (this.viewportWidth / 2) / this.zoom;
        const halfViewHeight = (this.viewportHeight / 2) / this.zoom;
        
        this.x = Math.max(halfViewWidth, Math.min(this.worldWidth - halfViewWidth, this.x));
        this.y = Math.max(halfViewHeight, Math.min(this.worldHeight - halfViewHeight, this.y));
        
        this.targetX = this.x;
        this.targetY = this.y;
    }
    
    updateTransform() {
        // Apply camera transform to container
        this.container.scale.set(this.zoom);
        this.container.x = -this.x * this.zoom + this.viewportWidth / 2 + this.shakeX;
        this.container.y = -this.y * this.zoom + this.viewportHeight / 2 + this.shakeY;
    }
    
    // Convert screen coordinates to world coordinates
    screenToWorld(screenX, screenY) {
        const worldX = (screenX - this.viewportWidth / 2 - this.shakeX) / this.zoom + this.x;
        const worldY = (screenY - this.viewportHeight / 2 - this.shakeY) / this.zoom + this.y;
        return { x: worldX, y: worldY };
    }
    
    // Convert world coordinates to screen coordinates
    worldToScreen(worldX, worldY) {
        const screenX = (worldX - this.x) * this.zoom + this.viewportWidth / 2 + this.shakeX;
        const screenY = (worldY - this.y) * this.zoom + this.viewportHeight / 2 + this.shakeY;
        return { x: screenX, y: screenY };
    }
    
    // Check if point is visible on screen
    isVisible(worldX, worldY, margin = 50) {
        const screen = this.worldToScreen(worldX, worldY);
        return screen.x >= -margin && 
               screen.x <= this.viewportWidth + margin &&
               screen.y >= -margin && 
               screen.y <= this.viewportHeight + margin;
    }
    
    // Get camera bounds in world coordinates
    getBounds() {
        const halfWidth = (this.viewportWidth / 2) / this.zoom;
        const halfHeight = (this.viewportHeight / 2) / this.zoom;
        
        return {
            left: this.x - halfWidth,
            right: this.x + halfWidth,
            top: this.y - halfHeight,
            bottom: this.y + halfHeight
        };
    }
    
    destroy() {
        this.target = null;
        if (this.container.parent) {
            this.container.parent.removeChild(this.container);
        }
        this.container.destroy();
        console.log('💥 Camera destroyed');
    }
}
