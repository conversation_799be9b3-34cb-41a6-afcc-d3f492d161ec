/**
 * Velocity Component
 * Handles movement speed and direction
 */

import { Component } from '../engine/ECS.js';

export class Velocity extends Component {
    constructor(x = 0, y = 0, maxSpeed = Infinity) {
        super();
        this.x = x;
        this.y = y;
        this.maxSpeed = maxSpeed;
        
        // Friction/damping
        this.friction = 0;
        this.airResistance = 0;
    }
    
    setVelocity(x, y) {
        this.x = x;
        this.y = y;
        this.clampToMaxSpeed();
    }
    
    addVelocity(dx, dy) {
        this.x += dx;
        this.y += dy;
        this.clampToMaxSpeed();
    }
    
    setSpeed(speed) {
        const currentSpeed = this.getSpeed();
        if (currentSpeed > 0) {
            const ratio = speed / currentSpeed;
            this.x *= ratio;
            this.y *= ratio;
        }
        this.clampToMaxSpeed();
    }
    
    getSpeed() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    
    getDirection() {
        return Math.atan2(this.y, this.x);
    }
    
    setDirection(angle, speed = null) {
        const currentSpeed = speed !== null ? speed : this.getSpeed();
        this.x = Math.cos(angle) * currentSpeed;
        this.y = Math.sin(angle) * currentSpeed;
    }
    
    clampToMaxSpeed() {
        const speed = this.getSpeed();
        if (speed > this.maxSpeed) {
            const ratio = this.maxSpeed / speed;
            this.x *= ratio;
            this.y *= ratio;
        }
    }
    
    // Apply friction
    applyFriction(deltaTime) {
        if (this.friction > 0) {
            const frictionForce = this.friction * deltaTime;
            const speed = this.getSpeed();
            
            if (speed > 0) {
                const frictionRatio = Math.max(0, 1 - (frictionForce / speed));
                this.x *= frictionRatio;
                this.y *= frictionRatio;
            }
        }
    }
    
    // Apply air resistance (proportional to velocity squared)
    applyAirResistance(deltaTime) {
        if (this.airResistance > 0) {
            const speed = this.getSpeed();
            if (speed > 0) {
                const resistance = this.airResistance * speed * speed * deltaTime;
                const resistanceRatio = Math.max(0, 1 - (resistance / speed));
                this.x *= resistanceRatio;
                this.y *= resistanceRatio;
            }
        }
    }
    
    // Stop movement
    stop() {
        this.x = 0;
        this.y = 0;
    }
    
    // Check if moving
    isMoving(threshold = 0.01) {
        return this.getSpeed() > threshold;
    }
    
    // Normalize velocity (set to unit vector)
    normalize() {
        const speed = this.getSpeed();
        if (speed > 0) {
            this.x /= speed;
            this.y /= speed;
        }
        return this;
    }
    
    // Copy from another velocity
    copy(other) {
        this.x = other.x;
        this.y = other.y;
        return this;
    }
    
    // Scale velocity
    scale(factor) {
        this.x *= factor;
        this.y *= factor;
        this.clampToMaxSpeed();
        return this;
    }
}
